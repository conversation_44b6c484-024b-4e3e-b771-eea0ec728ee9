import datetime
from os import name
import pytz

# https://github.com/psf/requests
import requests

# https://huggingface.co/docs/smolagents/index
# https://github.com/huggingface/smolagents
from smolagents import tool, ToolCallingAgent, OpenAIServerModel
from smolagents.agents import PromptTemplates, PlanningPromptTemplate, ManagedAgentPromptTemplate, FinalAnswerPromptTemplate

@tool
def get_public_holidays(country_code: str, year: int) -> str:
    """Fetch public holidays for a given country and year (no API key).
    Args:
        country_code: ISO 3166-1 alpha-2 code (e.g., "US", "BR", "GB").
        year: Four-digit year to query (e.g., 2025).
    Returns:
        A newline-separated list like "YYYY-MM-DD: Local Name". If none found,
        returns a short message describing the issue.
    """
    try:
        url = (
            f"https://date.nager.at/api/v3/PublicHolidays/{year}/{country_code.upper()}"
        )
        r = requests.get(url, timeout=10)
        r.raise_for_status()
        holidays = r.json()
        if not holidays:
            return f"No holidays found for {country_code.upper()} in {year}."
        lines = [f"{h['date']}: {h['localName']}" for h in holidays]
        return "\n".join(lines)
    except requests.RequestException as e:
        return f"Error fetching holidays: {e}"


@tool
def get_current_time_in_timezone(timezone: str) -> str:
    """
    Obtient l'heure actuelle dans un fuseau horaire spécifié.

    Args:
        timezone (str): Le fuseau horaire (ex: 'Europe/Paris')

    Returns:
        str: L'heure actuelle formatée
    """
    try:
        tz = pytz.timezone(timezone)
        local_time = datetime.datetime.now(tz).strftime("%Y-%m-%d %H:%M:%S")
        return f"The current local time in {timezone} is: {local_time}"
    except pytz.UnknownTimeZoneError as e:
        return f"Error: Unknown timezone '{timezone}': {str(e)}"
    except (ValueError, TypeError) as e:
        return f"Error fetching time for timezone '{timezone}': {str(e)}"


@tool
def get_eur_exchange_rate(currency_code: str) -> str:
    """A tool that retrieves the current exchange rate from EUR to a given currency

    Args:
        currency_code: The 3-letter currency code in lowercase (e.g., "usd", "gbp", "jpy")
    """

    try:
        # Fetch the EUR exchange rates from the API
        api_url = "https://cdn.jsdelivr.net/npm/@fawazahmed0/currency-api@latest/v1/currencies/eur.json"
        response = requests.get(api_url, timeout=10)
        response.raise_for_status()

        data = response.json()

        # Extract date and exchange rates
        date = data.get("date", "Unknown date")
        exchange_rates = data.get("eur", {})

        # Get the specific exchange rate
        if currency_code in exchange_rates:
            rate = exchange_rates[currency_code]
            return f"1 EUR = {rate} {currency_code.upper()} (as of {date})"
        else:
            return f"Currency code '{currency_code}' not found in the exchange rate data. Please check the currency code format."
    except requests.RequestException as e:
        return f"Error fetching exchange rate data: {str(e)}"
    except ValueError as e:
        return f"Error parsing exchange rate data: {str(e)}"


@tool
def get_simple_joke() -> str:
    """A tool that fetches a random joke from JokeAPI with minimal filtering"""

    try:
        api_url = "https://v2.jokeapi.dev/joke/any"

        response = requests.get(api_url, timeout=10)
        response.raise_for_status()

        joke_data = response.json()

        if joke_data.get("error", False):
            return f"Error: {joke_data.get('message', 'Could not get joke')}"

        if joke_data.get("type") == "single":
            return f"Here's a joke:\n\n{joke_data.get('joke', '')}"
        elif joke_data.get("type") == "twopart":
            setup = joke_data.get("setup", "")
            delivery = joke_data.get("delivery", "")
            return f"Here's a joke:\n\n{setup}\n{delivery}"

        return "Could not parse joke from API response"
    except requests.RequestException as e:
        return f"Error fetching joke: {str(e)}"
    except ValueError as e:
        return f"Error parsing joke data: {str(e)}"


if __name__ == "__main__":
    lmstudio_model = OpenAIServerModel(
        model_id="local-model",  # This can be any name, LM Studio will use whatever model you have loaded
        api_base="http://localhost:1234/v1",  # Default LM Studio API endpoint
        api_key="not-needed",  # LM Studio doesn't require an API key by default
        temperature=0.1,
    )

    agents_manager = ToolCallingAgent(
        tools=[get_public_holidays, get_current_time_in_timezone, get_eur_exchange_rate, get_simple_joke],
        model=lmstudio_model,
        max_steps=10,
        stream_outputs=True,
        prompt_templates=PromptTemplates(
            system_prompt="",
            planning=PlanningPromptTemplate(
                initial_plan="",
                update_plan_pre_messages="",
                update_plan_post_messages="",
            ),
            managed_agent=ManagedAgentPromptTemplate(task="", report=""),
            final_answer=FinalAnswerPromptTemplate(pre_messages="", post_messages=""),
        )
    )

    result = agents_manager.run(
        "Quelle heure est-il à Paris et quel est le taux de change de l'euro vers le dollar ? En 2025, quels sont les jours fériés en France ?"
    )

    print(result)

    # =================================================================================================================================================

    """prompt_templates=PromptTemplates(
            system_prompt="Tu es un agent spécialisé dans la récupération de données variées. Tu dois être aussi précis que possible."
            planning=PlanningPromptTemplate(
                initial_plan="Tu dois créer un plan pour répondre à la tâche. Le plan doit être détaillé et étape par étape.",
            ),
            managed_agent=ManagedAgentPromptTemplate(
                task="Tu dois exécuter le plan pour répondre à la tâche. Tu dois être aussi précis que possible."
            )
            final_answer=FinalAnswerPromptTemplate(
                pre_messages="Tu dois fournir une réponse finale à la tâche. Tu dois être aussi précis que possible."
            )
    )"""

    """
    # Create a LiteLLM model with Ollama
    ollama_model = LiteLLMModel(
        model_id="ollama_chat/gpt-oss:20b",
        api_base="https://ollama.com:443",
        api_key="447d9b4a99e745478aa557a1421b8448.sw7ANALKpswZmgEltzRctsGX",
        num_ctx=8192,
    )

    agent = ToolCallingAgent(
        tools=[get_public_holidays],
        model=ollama_model
        #model="lm-studio/lmstudio-ai-llm-13b"
    )
    """
